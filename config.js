// Extension configuration
// This file controls console logging and other development/production settings
// Set isDevelopment to false when building for production

window.CONFIG = {
  // Set this to false for production builds
  isDevelopment: true,
  
  // You can also add other development-specific settings here
  enableDebugMode: true,
  enableVerboseLogging: true
};

// Override console.log to only work in development mode
(function() {
  // Store the original console methods
  const originalConsole = {
    log: console.log,
    warn: console.warn,
    error: console.error,
    info: console.info,
    debug: console.debug
  };
  
  // Override console.log
  console.log = function(...args) {
    if (window.CONFIG?.isDevelopment) {
      originalConsole.log.apply(console, args);
    }
  };

  // Override console.info (optional - you might want to keep info in production)
  console.info = function(...args) {
    if (window.CONFIG?.isDevelopment) {
      originalConsole.info.apply(console, args);
    }
  };

  // Override console.debug
  console.debug = function(...args) {
    if (window.CONFIG?.isDevelopment && window.CONFIG?.enableDebugMode) {
      originalConsole.debug.apply(console, args);
    }
  };
  
  // Keep console.warn and console.error always active as they're important for production
  // But you can override them too if needed:
  // console.warn = function(...args) {
  //   if (window.CONFIG?.isDevelopment) {
  //     originalConsole.warn.apply(console, args);
  //   }
  // };
  //
  // console.error = function(...args) {
  //   if (window.CONFIG?.isDevelopment) {
  //     originalConsole.error.apply(console, args);
  //   }
  // };
})();
