# Icons for YouTube Reading Friendly Extension

This directory should contain the following icon files:

1. `icon16.png` - 16x16 pixels
2. `icon48.png` - 48x48 pixels
3. `icon128.png` - 128x128 pixels
4. `icon_active_16.png` - 16x16 pixels (active state)
5. `icon_active_48.png` - 48x48 pixels (active state)
6. `icon_active_128.png` - 128x128 pixels (active state)

## Icon Design Guidelines

- Regular icons should use a neutral color scheme
- Active state icons should use a red accent color (#cc0000) to indicate the extension is enabled
- Icons should be simple and recognizable at small sizes
- Suggested design: A stylized "YT" with a book or text symbol

## Creating Icons

You can create these icons using any image editing software like:
- Adobe Photoshop
- GIMP (free)
- Figma
- Sketch

## Placeholder Icons

Until you create custom icons, you can use placeholder icons from various icon libraries:
1. [Material Design Icons](https://material.io/resources/icons/)
2. [Font Awesome](https://fontawesome.com/)
3. [Feather Icons](https://feathericons.com/)

## Icon Generation Tools

You can also use online tools to generate icon sets from a single image:
- [App Icon Generator](https://appicon.co/)
- [Favicon Generator](https://realfavicongenerator.net/)
