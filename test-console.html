<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Console.log Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            cursor: pointer;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .dev-mode { background-color: #d4edda; color: #155724; }
        .prod-mode { background-color: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Console.log Development Mode Test</h1>
    
    <div class="test-section">
        <h2>Current Configuration</h2>
        <div id="config-status" class="status"></div>
    </div>
    
    <div class="test-section">
        <h2>Test Console Methods</h2>
        <p>Open your browser's Developer Tools (F12) and check the Console tab, then click these buttons:</p>
        
        <button onclick="testConsoleLog()">Test console.log()</button>
        <button onclick="testConsoleInfo()">Test console.info()</button>
        <button onclick="testConsoleDebug()">Test console.debug()</button>
        <button onclick="testConsoleWarn()">Test console.warn()</button>
        <button onclick="testConsoleError()">Test console.error()</button>
        
        <div style="margin-top: 15px;">
            <button onclick="runAllTests()">Run All Tests</button>
        </div>
    </div>
    
    <div class="test-section">
        <h2>Expected Behavior</h2>
        <div id="expected-behavior"></div>
    </div>

    <script src="config.js"></script>
    <script>
        // Update configuration status
        function updateConfigStatus() {
            const statusDiv = document.getElementById('config-status');
            const expectedDiv = document.getElementById('expected-behavior');
            
            if (window.CONFIG) {
                const isDev = window.CONFIG.isDevelopment;
                const debugMode = window.CONFIG.enableDebugMode;
                
                statusDiv.className = `status ${isDev ? 'dev-mode' : 'prod-mode'}`;
                statusDiv.innerHTML = `
                    <strong>Mode:</strong> ${isDev ? 'DEVELOPMENT' : 'PRODUCTION'}<br>
                    <strong>isDevelopment:</strong> ${isDev}<br>
                    <strong>enableDebugMode:</strong> ${debugMode}<br>
                    <strong>enableVerboseLogging:</strong> ${window.CONFIG.enableVerboseLogging}
                `;
                
                expectedDiv.innerHTML = `
                    <ul>
                        <li><strong>console.log():</strong> ${isDev ? '✅ Should appear' : '❌ Should be silent'}</li>
                        <li><strong>console.info():</strong> ${isDev ? '✅ Should appear' : '❌ Should be silent'}</li>
                        <li><strong>console.debug():</strong> ${isDev && debugMode ? '✅ Should appear' : '❌ Should be silent'}</li>
                        <li><strong>console.warn():</strong> ✅ Should always appear</li>
                        <li><strong>console.error():</strong> ✅ Should always appear</li>
                    </ul>
                `;
            } else {
                statusDiv.className = 'status prod-mode';
                statusDiv.innerHTML = '<strong>Error:</strong> CONFIG not found!';
            }
        }
        
        // Test functions
        function testConsoleLog() {
            console.log('🔍 This is a console.log() test message');
        }
        
        function testConsoleInfo() {
            console.info('ℹ️ This is a console.info() test message');
        }
        
        function testConsoleDebug() {
            console.debug('🐛 This is a console.debug() test message');
        }
        
        function testConsoleWarn() {
            console.warn('⚠️ This is a console.warn() test message');
        }
        
        function testConsoleError() {
            console.error('❌ This is a console.error() test message');
        }
        
        function runAllTests() {
            console.log('=== Running All Console Tests ===');
            testConsoleLog();
            testConsoleInfo();
            testConsoleDebug();
            testConsoleWarn();
            testConsoleError();
            console.log('=== End of Console Tests ===');
        }
        
        // Initialize
        updateConfigStatus();
    </script>
</body>
</html>
