* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f5f5;
  color: #333;
}

.container {
  width: 300px;
  padding: 16px;
}

h1 {
  font-size: 18px;
  margin-bottom: 16px;
  color: #cc0000;
  text-align: center;
}

h2 {
  font-size: 16px;
  margin: 12px 0;
}

.toggle-container {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.toggle-label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.toggle-label span {
  margin-right: 10px;
}

.toggle-container input[type="checkbox"] {
  height: 0;
  width: 0;
  visibility: hidden;
  position: absolute;
}

.toggle-slider {
  position: relative;
  width: 50px;
  height: 24px;
  background-color: #ccc;
  border-radius: 24px;
  transition: 0.3s;
}

.toggle-slider:after {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background-color: white;
  border-radius: 50%;
  transition: 0.3s;
}

input:checked + .toggle-slider {
  background-color: #cc0000;
}

input:checked + .toggle-slider:after {
  left: calc(100% - 2px);
  transform: translateX(-100%);
}

.settings {
  background-color: white;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.setting-item {
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.font-weight-section {
  flex-direction: column;
  align-items: flex-start;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  margin-top: 8px;
  width: 100%;
}

.checkbox-item {
  display: flex;
  align-items: center;
  margin-right: 12px;
  margin-bottom: 6px;
}

.checkbox-item input[type="checkbox"] {
  margin-right: 5px;
  width: 16px;
  height: 16px;
  visibility: visible;
  position: relative;
}

.checkbox-item label {
  font-size: 13px;
  cursor: pointer;
}

.setting-note {
  margin: 12px 0;
  padding: 8px;
  background-color: #f5f5f5;
  border-left: 3px solid #cc0000;
  border-radius: 4px;
}

.setting-note p {
  margin: 0;
  font-size: 12px;
  color: #666;
  font-style: italic;
}

select {
  padding: 6px;
  border-radius: 4px;
  border: 1px solid #ddd;
  background-color: white;
  width: 120px;
}

.buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 10px;
}

button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

#reset-btn {
  background-color: #cc0000;
  color: white;
  min-width: 130px;
}

#reset-btn:hover {
  background-color: #aa0000;
}

.advanced-link {
  color: #666;
  text-align: center;
  text-decoration: none;
  font-size: 13px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.advanced-link:hover {
  color: #cc0000;
  background-color: #f0f0f0;
  text-decoration: none;
}

.footer {
  text-align: center;
  font-size: 12px;
  margin-top: 8px;
}

/* Status indicator for auto-save feedback */
.status-indicator {
  position: fixed;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #cc0000;
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  opacity: 0;
  transition: opacity 0.3s ease, transform 0.2s ease;
  pointer-events: none;
  z-index: 1000;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.status-indicator[style*="opacity: 1"] {
  transform: translateX(-50%) translateY(-5px);
}
