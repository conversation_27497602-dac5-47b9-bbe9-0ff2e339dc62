# YouTube Reading Friendly Chrome Extension

A Chrome extension that enhances the reading experience on YouTube by improving readability and reducing distractions.

## Features

- **Reading Mode**: Transform YouTube pages for better readability
- **Customizable Font Settings**: Adjust font size, line spacing, and more
- **Theme Options**: Choose between light, dark, and sepia themes
- **Content Filtering**: Hide distracting elements like sidebars and recommendations
- **Focus Mode**: Dim the background to focus on the content
- **Keyboard Shortcuts**: Toggle reading mode with a keyboard shortcut

## Installation

### From Chrome Web Store (Coming Soon)

1. Visit the Chrome Web Store (link to be added)
2. Click "Add to Chrome"
3. Confirm the installation

### Manual Installation (Developer Mode)

1. Download or clone this repository
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode" in the top-right corner
4. Click "Load unpacked" and select the extension directory
5. The extension should now be installed and visible in your toolbar

## Usage

1. Click the extension icon in the toolbar to open the popup
2. Toggle "Enable Reading Mode" to activate the extension
3. Adjust settings according to your preferences
4. Click "Save Settings" to apply changes
5. Use the keyboard shortcut (default: Alt+R) to quickly toggle reading mode

## Advanced Options

Access advanced options by clicking "Advanced Options" in the popup or right-clicking the extension icon and selecting "Options".

Advanced settings include:
- Font family and weight
- Content max width
- Paragraph spacing
- Element visibility controls
- Focus mode settings
- Keyboard shortcut customization
- Import/export settings

## Development

### Project Structure

```
youtube-reading-friendly/
├── manifest.json        # Extension manifest
├── popup.html           # Popup interface
├── popup.css            # Popup styles
├── popup.js             # Popup functionality
├── options.html         # Options page
├── options.css          # Options styles
├── options.js           # Options functionality
├── content.js           # Content script for YouTube pages
├── styles.css           # Injected styles
├── background.js        # Background service worker
└── icons/               # Extension icons
    ├── icon16.png
    ├── icon48.png
    ├── icon128.png
    ├── icon_active_16.png
    ├── icon_active_48.png
    └── icon_active_128.png
```

### Building and Testing

1. Make changes to the code
2. Test the extension by loading it as an unpacked extension
3. Reload the extension after making changes

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Inspired by reader mode features in modern browsers
- Thanks to the Chrome Extensions API documentation
