<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Auto-Open Live Chat Feature</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            border: 1px solid #ddd;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }
        .result {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            cursor: pointer;
        }
        .chat-panel {
            background: #e8f4fd;
            border: 2px solid #1976d2;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .sidebar {
            background: #f0f0f0;
            border: 1px solid #ccc;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .hidden {
            display: none !important;
        }
    </style>
</head>
<body>
    <h1>Test Auto-Open Live Chat Feature</h1>
    
    <div class="test-section">
        <h3>Feature Overview</h3>
        <p>This feature automatically opens live chat when the "Hide Related videos Sidebar" option is enabled and the sidebar is actually hidden (contains only related videos).</p>
        
        <h4>Settings:</h4>
        <label>
            <input type="checkbox" id="hideSidebarTest" checked>
            Hide Related videos Sidebar
        </label><br>
        <label>
            <input type="checkbox" id="autoOpenChatTest" style="margin-left: 20px;">
            Auto-open live chat when sidebar is hidden
        </label>
    </div>

    <div class="test-section">
        <h3>Simulated YouTube Elements</h3>
        
        <div id="secondary" class="sidebar">
            <h4>Sidebar (#secondary)</h4>
            <div id="other-content" style="background: #eeffee; padding: 10px; margin: 5px 0;">
                Other Sidebar Content (chat, playlist, etc.)
            </div>
            <ytd-watch-next-secondary-results-renderer>
                <div style="background: #ffeeee; padding: 10px; margin: 5px 0;">
                    Related Videos Section
                </div>
            </ytd-watch-next-secondary-results-renderer>
        </div>
        
        <div id="chat-container">
            <!-- Chat will be added here when auto-opened -->
        </div>

        <h4>Test Controls:</h4>
        <button onclick="testAutoOpenChat()">Test Auto-Open Chat Logic</button>
        <button onclick="addChatPanel()">Manually Add Chat Panel</button>
        <button onclick="removeChatPanel()">Remove Chat Panel</button>
        <button onclick="hideOtherContent()">Hide Other Sidebar Content</button>
        <button onclick="showOtherContent()">Show Other Sidebar Content</button>
        
        <div id="test-result" class="result">
            Test results will appear here...
        </div>
    </div>

    <div class="test-section">
        <h3>Live Chat Button Simulation</h3>
        <p>These buttons simulate YouTube's live chat toggle buttons:</p>
        <button aria-label="Show chat" onclick="simulateShowChat()">Show Chat Button</button>
        <button aria-label="Hide chat" onclick="simulateHideChat()" style="display: none;">Hide Chat Button</button>
        
        <div id="chat-simulation-result" class="result">
            Chat button simulation results...
        </div>
    </div>

    <script>
        // Copy the relevant functions from content.js for testing
        const currentSettings = {
            hideSidebar: true,
            autoOpenChat: false
        };

        function hasChatPanel() {
            const liveChatFrame = document.querySelector('ytd-live-chat-frame');
            const liveChatRenderer = document.querySelector('ytd-live-chat-renderer');
            const chatFrame = document.querySelector('#chatframe');
            const chatContainer = document.querySelector('#chat-container ytd-live-chat-frame');

            return !!(liveChatFrame || liveChatRenderer || chatFrame || chatContainer);
        }

        function isLiveChatAvailable() {
            const chatToggleButton = document.querySelector('[aria-label*="chat" i], [aria-label*="Show chat" i], [aria-label*="Hide chat" i]');
            const liveBadge = document.querySelector('.ytp-live-badge, .badge-style-type-live-now');
            const chatReplayButton = document.querySelector('[aria-label*="chat replay" i]');
            
            return !!(chatToggleButton || liveBadge || chatReplayButton);
        }

        function sidebarContainsOnlyRelatedVideos() {
            const sidebar = document.querySelector('#secondary');
            if (!sidebar) return false;

            const isElementVisible = (selector) => {
                const elements = sidebar.querySelectorAll(selector);
                return Array.from(elements).some(el =>
                    !el.hasAttribute('hidden') &&
                    el.style.display !== 'none' &&
                    getComputedStyle(el).display !== 'none'
                );
            };

            const hasOtherContent = isElementVisible('#other-content');
            const hasChat = isElementVisible('ytd-live-chat-frame, ytd-live-chat-renderer, #chatframe');

            if (hasOtherContent || hasChat) {
                return false;
            }

            const hasRelatedVideos = isElementVisible('ytd-watch-next-secondary-results-renderer');
            return !!hasRelatedVideos;
        }

        function getHidingStrategy() {
            const setting = currentSettings.hideSidebar;
            const shouldHideRelatedVideos = setting === true || setting === 'always';
            
            if (!shouldHideRelatedVideos) {
                return { hideRelatedVideos: false, hideSidebar: false };
            }

            const hasOtherContent = !sidebarContainsOnlyRelatedVideos();
            return {
                hideRelatedVideos: true,
                hideSidebar: !hasOtherContent
            };
        }

        function autoOpenLiveChat() {
            if (!currentSettings.autoOpenChat) {
                return 'Auto-open chat is disabled';
            }

            if (hasChatPanel()) {
                return 'Chat panel already visible, skipping auto-open';
            }

            if (!isLiveChatAvailable()) {
                return 'Live chat not available for this video';
            }

            // Try to find and click the chat toggle button
            const chatToggleSelectors = [
                '[aria-label*="Show chat" i]',
                '[aria-label*="chat" i]:not([aria-label*="Hide chat" i])',
                'button[aria-label*="Show chat" i]'
            ];

            for (const selector of chatToggleSelectors) {
                const button = document.querySelector(selector);
                if (button && button.offsetParent !== null) {
                    button.click();
                    return `Auto-opened live chat using selector: ${selector}`;
                }
            }

            return 'No suitable chat toggle button found';
        }

        function testAutoOpenChat() {
            // Update settings from checkboxes
            currentSettings.hideSidebar = document.getElementById('hideSidebarTest').checked;
            currentSettings.autoOpenChat = document.getElementById('autoOpenChatTest').checked;

            const strategy = getHidingStrategy();
            let result = `Settings: hideSidebar=${currentSettings.hideSidebar}, autoOpenChat=${currentSettings.autoOpenChat}\n`;
            result += `Strategy: hideRelatedVideos=${strategy.hideRelatedVideos}, hideSidebar=${strategy.hideSidebar}\n`;
            result += `Has chat panel: ${hasChatPanel()}\n`;
            result += `Is live chat available: ${isLiveChatAvailable()}\n`;
            result += `Sidebar contains only related videos: ${sidebarContainsOnlyRelatedVideos()}\n`;

            if (strategy.hideSidebar && currentSettings.autoOpenChat) {
                const autoOpenResult = autoOpenLiveChat();
                result += `Auto-open result: ${autoOpenResult}`;
            } else {
                result += 'Auto-open conditions not met';
            }

            document.getElementById('test-result').textContent = result;
        }

        function addChatPanel() {
            const container = document.getElementById('chat-container');
            container.innerHTML = '<ytd-live-chat-frame class="chat-panel">🎬 Live Chat Panel (Simulated)</ytd-live-chat-frame>';
            testAutoOpenChat();
        }

        function removeChatPanel() {
            const container = document.getElementById('chat-container');
            container.innerHTML = '';
            testAutoOpenChat();
        }

        function hideOtherContent() {
            const otherContent = document.getElementById('other-content');
            if (otherContent) {
                otherContent.setAttribute('hidden', '');
            }
            testAutoOpenChat();
        }

        function showOtherContent() {
            const otherContent = document.getElementById('other-content');
            if (otherContent) {
                otherContent.removeAttribute('hidden');
                otherContent.style.display = 'block';
            }
            testAutoOpenChat();
        }

        function simulateShowChat() {
            const showBtn = document.querySelector('[aria-label="Show chat"]');
            const hideBtn = document.querySelector('[aria-label="Hide chat"]');
            
            // Simulate adding chat panel
            addChatPanel();
            
            // Toggle button visibility
            showBtn.style.display = 'none';
            hideBtn.style.display = 'inline-block';
            
            document.getElementById('chat-simulation-result').textContent = 'Show chat button clicked - chat panel added';
        }

        function simulateHideChat() {
            const showBtn = document.querySelector('[aria-label="Show chat"]');
            const hideBtn = document.querySelector('[aria-label="Hide chat"]');
            
            // Simulate removing chat panel
            removeChatPanel();
            
            // Toggle button visibility
            showBtn.style.display = 'inline-block';
            hideBtn.style.display = 'none';
            
            document.getElementById('chat-simulation-result').textContent = 'Hide chat button clicked - chat panel removed';
        }

        // Update auto-open chat checkbox state based on hide sidebar checkbox
        document.getElementById('hideSidebarTest').addEventListener('change', function() {
            const autoOpenCheckbox = document.getElementById('autoOpenChatTest');
            autoOpenCheckbox.disabled = !this.checked;
            if (!this.checked) {
                autoOpenCheckbox.checked = false;
            }
            testAutoOpenChat();
        });

        document.getElementById('autoOpenChatTest').addEventListener('change', testAutoOpenChat);

        // Initial test
        testAutoOpenChat();
    </script>
</body>
</html>
