# Auto-Open Live Chat Feature

## Overview
This feature automatically opens YouTube's live chat when the "Hide Related videos Sidebar" option is enabled and the sidebar is actually hidden (contains only related videos).

## Implementation Details

### Files Modified

#### 1. `options.html`
- Added new checkbox for "Auto-open live chat when sidebar is hidden"
- Positioned as a sub-option under "Hide Related videos Sidebar" with indentation

#### 2. `options.js`
- Added `autoOpenChat` to default settings (default: `false`)
- Added checkbox element reference and event handling
- Updated `loadSettings()` to load the new setting
- Updated `saveSettings()` to save the new setting
- Modified `updateUI()` to disable auto-open chat when hide sidebar is unchecked
- Added event listener for hide sidebar checkbox to update UI dependencies

#### 3. `content.js`
- Added `autoOpenChat` to storage initialization and currentSettings
- Created `isLiveChatAvailable()` function to detect if live chat is available
- Created `autoOpenLiveChat()` function to automatically open live chat
- Modified `getHidingStrategy()` to trigger auto-open chat when sidebar is hidden
- Updated settings update handler to trigger auto-open chat when settings change
- Modified `observePageChanges()` to trigger auto-open chat on page navigation

### Key Functions

#### `isLiveChatAvailable()`
Detects if live chat is available for the current video by checking for:
- Chat toggle buttons with aria-labels containing "chat"
- Live stream badges
- Chat replay buttons

#### `autoOpenLiveChat()`
Attempts to open live chat by:
1. Checking if auto-open is enabled
2. Verifying we're on a video page
3. Checking if chat is already visible
4. Verifying live chat is available
5. Finding and clicking appropriate chat toggle buttons
6. Fallback: Showing hidden chat containers

### Logic Flow

1. **User enables "Hide Related videos Sidebar"** → Sidebar hiding logic activates
2. **Sidebar contains only related videos** → Entire sidebar gets hidden
3. **Auto-open chat is enabled** → `autoOpenLiveChat()` is triggered
4. **Live chat is available** → Chat toggle button is clicked to show chat
5. **Chat panel appears** → User can interact with live chat while sidebar is hidden

### UI Dependencies

- Auto-open chat checkbox is disabled when "Hide Related videos Sidebar" is unchecked
- Auto-open chat is automatically unchecked when hide sidebar is disabled
- This ensures the feature only works when it makes sense contextually

### Timing Considerations

- **Initial page load**: 1000ms delay to ensure DOM is ready
- **Settings update**: 1000ms delay to allow for UI updates
- **Page navigation**: 2000ms delay to ensure all YouTube elements are loaded

### Browser Compatibility

The feature uses standard DOM APIs and should work in all modern browsers:
- `querySelector()` for element selection
- `click()` for button interaction
- `setTimeout()` for timing control
- Chrome extension storage API for settings

### Testing

A test file `test_auto_open_chat.html` is provided to verify the functionality:
- Simulates YouTube's DOM structure
- Tests the logic functions
- Provides interactive controls to test different scenarios
- Shows real-time results of the auto-open logic

### Error Handling

- Gracefully handles missing DOM elements
- Checks for element visibility before interaction
- Provides console logging for debugging
- Falls back to alternative methods if primary approach fails

### Performance Impact

- Minimal performance impact as functions only run when conditions are met
- Uses efficient DOM queries with specific selectors
- Implements proper timing to avoid blocking the main thread
- Only activates on video pages when sidebar is actually hidden

## Usage

1. Go to extension options
2. Enable "Hide Related videos Sidebar"
3. Enable "Auto-open live chat when sidebar is hidden"
4. Visit a YouTube video with live chat or chat replay
5. The sidebar will be hidden and live chat will automatically open

## Future Enhancements

Potential improvements could include:
- Support for different chat types (live, replay, premiere)
- User preference for chat position/size
- Integration with theater mode
- Support for mobile YouTube interface
- Custom delay settings for different connection speeds
