// Build Scripts for YouTube Reading Friendly Extension
// Run with Node.js: node build-scripts.js [dev|prod]

const fs = require('fs');
const path = require('path');

const CONFIG_FILE = 'config.js';

// Development configuration
const DEV_CONFIG = `// Extension configuration
// This file controls console logging and other development/production settings
// Set isDevelopment to false when building for production

window.CONFIG = {
  // Set this to false for production builds
  isDevelopment: true,
  
  // You can also add other development-specific settings here
  enableDebugMode: true,
  enableVerboseLogging: true
};

// Override console.log to only work in development mode
(function() {
  // Store the original console methods
  const originalConsole = {
    log: console.log,
    warn: console.warn,
    error: console.error,
    info: console.info,
    debug: console.debug
  };
  
  // Override console.log
  console.log = function(...args) {
    if (window.CONFIG?.isDevelopment) {
      originalConsole.log.apply(console, args);
    }
  };

  // Override console.info (optional - you might want to keep info in production)
  console.info = function(...args) {
    if (window.CONFIG?.isDevelopment) {
      originalConsole.info.apply(console, args);
    }
  };

  // Override console.debug
  console.debug = function(...args) {
    if (window.CONFIG?.isDevelopment && window.CONFIG?.enableDebugMode) {
      originalConsole.debug.apply(console, args);
    }
  };
  
  // Keep console.warn and console.error always active as they're important for production
  // But you can override them too if needed:
  /*
  console.warn = function(...args) {
    if (window.CONFIG?.isDevelopment) {
      originalConsole.warn.apply(console, args);
    }
  };

  console.error = function(...args) {
    if (window.CONFIG?.isDevelopment) {
      originalConsole.error.apply(console, args);
    }
  };
  */
})();`;

// Production configuration
const PROD_CONFIG = `// Extension configuration
// This file controls console logging and other development/production settings
// Set isDevelopment to false when building for production

window.CONFIG = {
  // Set this to false for production builds
  isDevelopment: false,
  
  // You can also add other development-specific settings here
  enableDebugMode: false,
  enableVerboseLogging: false
};

// Override console.log to only work in development mode
(function() {
  // Store the original console methods
  const originalConsole = {
    log: console.log,
    warn: console.warn,
    error: console.error,
    info: console.info,
    debug: console.debug
  };
  
  // Override console.log
  console.log = function(...args) {
    if (window.CONFIG?.isDevelopment) {
      originalConsole.log.apply(console, args);
    }
  };

  // Override console.info (optional - you might want to keep info in production)
  console.info = function(...args) {
    if (window.CONFIG?.isDevelopment) {
      originalConsole.info.apply(console, args);
    }
  };

  // Override console.debug
  console.debug = function(...args) {
    if (window.CONFIG?.isDevelopment && window.CONFIG?.enableDebugMode) {
      originalConsole.debug.apply(console, args);
    }
  };
  
  // Keep console.warn and console.error always active as they're important for production
  // But you can override them too if needed:
  /*
  console.warn = function(...args) {
    if (window.CONFIG?.isDevelopment) {
      originalConsole.warn.apply(console, args);
    }
  };

  console.error = function(...args) {
    if (window.CONFIG?.isDevelopment) {
      originalConsole.error.apply(console, args);
    }
  };
  */
})();`;

function setDevelopmentMode() {
  fs.writeFileSync(CONFIG_FILE, DEV_CONFIG);
  console.log('✅ Set to DEVELOPMENT mode - console.log enabled');
}

function setProductionMode() {
  fs.writeFileSync(CONFIG_FILE, PROD_CONFIG);
  console.log('✅ Set to PRODUCTION mode - console.log disabled');
}

function showCurrentMode() {
  try {
    const content = fs.readFileSync(CONFIG_FILE, 'utf8');
    const isDev = content.includes('isDevelopment: true');
    console.log(`Current mode: ${isDev ? 'DEVELOPMENT' : 'PRODUCTION'}`);
    console.log(`Console.log is: ${isDev ? 'ENABLED' : 'DISABLED'}`);
  } catch (error) {
    console.log('❌ Could not read config.js');
  }
}

// Main script
const args = process.argv.slice(2);
const command = args[0];

switch (command) {
  case 'dev':
  case 'development':
    setDevelopmentMode();
    break;
  case 'prod':
  case 'production':
    setProductionMode();
    break;
  case 'status':
  case 'check':
    showCurrentMode();
    break;
  default:
    console.log('YouTube Reading Friendly Extension - Build Scripts');
    console.log('');
    console.log('Usage:');
    console.log('  node build-scripts.js dev        - Set development mode (console.log enabled)');
    console.log('  node build-scripts.js prod       - Set production mode (console.log disabled)');
    console.log('  node build-scripts.js status     - Show current mode');
    console.log('');
    showCurrentMode();
}
