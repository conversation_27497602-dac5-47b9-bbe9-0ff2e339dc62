document.addEventListener('DOMContentLoaded', function() {
  // Get DOM elements
  const readingModeToggle = document.getElementById('reading-mode-toggle');
  const focusModeToggle = document.getElementById('focus-mode-toggle');
  const fontSizeSelect = document.getElementById('font-size');
  const htmlFontSizeSelect = document.getElementById('html-font-size');
  const resetButton = document.getElementById('reset-btn');
  const statusIndicator = document.createElement('div');

  // Add status indicator to the UI
  statusIndicator.className = 'status-indicator';
  document.querySelector('.container').appendChild(statusIndicator);

  // Default settings
  const defaultSettings = {
    readingMode: false,
    focusMode: false,
    fontSize: 'medium',
    htmlFontSize: 'default'
  };

  // Variable to track if settings have changed
  let settingsChanged = false;

  // Debounce function to prevent too many saves
  let saveTimeout = null;

  // Function to collect current settings
  function getCurrentSettings() {
    return {
      readingMode: readingModeToggle.checked,
      focusMode: focusModeToggle.checked,
      fontSize: fontSizeSelect.value,
      htmlFontSize: htmlFontSizeSelect.value
    };
  }

  // Function to save settings and apply them
  function saveSettings(showConfirmation = true) {
    const settings = getCurrentSettings();

    // Save to Chrome storage
    chrome.storage.sync.set(settings, function() {
      if (showConfirmation) {
        // Show save confirmation
        statusIndicator.textContent = 'Changes saved!';
        statusIndicator.style.opacity = '1';

        // Hide confirmation after 1.5 seconds
        setTimeout(() => {
          statusIndicator.style.opacity = '0';
        }, 1500);
      }

      // Apply settings to current tab
      chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        if (tabs && tabs[0]) {
          chrome.tabs.sendMessage(tabs[0].id, {
            action: 'updateSettings',
            settings: settings
          });
        }
      });

      // Reset the changed flag
      settingsChanged = false;
    });
  }

  // Function to auto-save settings with debounce
  function autoSaveSettings() {
    settingsChanged = true;

    // Clear any existing timeout
    if (saveTimeout) {
      clearTimeout(saveTimeout);
    }

    // Set a new timeout to save after 500ms of inactivity
    saveTimeout = setTimeout(() => {
      if (settingsChanged) {
        saveSettings(true);
      }
    }, 500);
  }

  // Load saved settings
  chrome.storage.sync.get(defaultSettings, function(settings) {
    // Apply saved settings to popup UI
    readingModeToggle.checked = settings.readingMode;
    focusModeToggle.checked = settings.focusMode;
    fontSizeSelect.value = settings.fontSize;
    htmlFontSizeSelect.value = settings.htmlFontSize;
  });

  // Toggle reading mode on the current tab and auto-save
  readingModeToggle.addEventListener('change', function() {
    const enabled = readingModeToggle.checked;

    // Update the icon to show active/inactive state
    updateIcon(enabled);

    // Apply changes to the current tab
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs && tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, {
          action: 'toggleReadingMode',
          enabled: enabled
        });
      }
    });

    // Auto-save the settings
    autoSaveSettings();
  });

  // Toggle focus mode on the current tab and auto-save
  focusModeToggle.addEventListener('change', function() {
    const enabled = focusModeToggle.checked;

    // Apply changes to the current tab
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
      if (tabs && tabs[0]) {
        chrome.tabs.sendMessage(tabs[0].id, {
          action: 'toggleFocusMode',
          enabled: enabled
        });
      }
    });

    // Auto-save the settings
    autoSaveSettings();
  });

  // Auto-save when font size changes
  fontSizeSelect.addEventListener('change', autoSaveSettings);

  // Auto-save when HTML font size changes
  htmlFontSizeSelect.addEventListener('change', autoSaveSettings);

  // Reset to defaults
  resetButton.addEventListener('click', function() {
    // Reset UI
    readingModeToggle.checked = defaultSettings.readingMode;
    focusModeToggle.checked = defaultSettings.focusMode;
    fontSizeSelect.value = defaultSettings.fontSize;
    htmlFontSizeSelect.value = defaultSettings.htmlFontSize;

    // Save default settings
    chrome.storage.sync.set(defaultSettings, function() {
      // Show confirmation
      statusIndicator.textContent = 'Reset to defaults!';
      statusIndicator.style.opacity = '1';

      // Hide confirmation after 1.5 seconds
      setTimeout(() => {
        statusIndicator.style.opacity = '0';
      }, 1500);

      // Apply default settings to current tab
      chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        if (tabs && tabs[0]) {
          chrome.tabs.sendMessage(tabs[0].id, {
            action: 'updateSettings',
            settings: defaultSettings
          });
        }
      });
    });
  });

  // Function to update the extension icon
  function updateIcon(enabled) {
    const iconPath = enabled ? 'icons/icon_active_' : 'icons/icon_';
    chrome.action.setIcon({
      path: {
        16: `${iconPath}16.png`,
        48: `${iconPath}48.png`,
        128: `${iconPath}128.png`
      }
    });
  }
});
