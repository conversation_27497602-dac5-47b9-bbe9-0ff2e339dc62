<!DOCTYPE html>
<html>
<head>
    <title>Sidebar Hide Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { font-weight: bold; color: #007cba; }
        button { margin: 5px; padding: 8px 15px; }
        #secondary { background: #f0f0f0; padding: 10px; margin: 10px 0; }
        .chat-panel { background: #e0f0ff; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>YouTube Reading Friendly - Smart Sidebar Hide Test</h1>
    
    <div class="test-section">
        <h3>Test Chat Panel Detection</h3>
        <button onclick="addChatPanel()">Add Chat Panel</button>
        <button onclick="removeChatPanel()">Remove Chat Panel</button>
        <button onclick="testChatDetection()">Test Detection</button>
        <div id="chat-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test Smart Sidebar Hiding Logic</h3>
        <label>
            <input type="checkbox" id="hideSidebarTest" onchange="testSidebarLogic()" checked>
            Hide related videos (smart sidebar hiding)
        </label>
        <div id="sidebar-result" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Simulated Elements</h3>
        <div id="secondary">
            <div id="other-content" style="background: #eeffee; padding: 10px; margin: 5px 0;">
                Other Sidebar Content (chat, playlist, etc.)
            </div>
            <ytd-watch-next-secondary-results-renderer>
                <div style="background: #ffeeee; padding: 10px; margin: 5px 0;">
                    Related Videos Section
                </div>
            </ytd-watch-next-secondary-results-renderer>
        </div>
        <div id="chat-container"></div>

        <h4>Controls:</h4>
        <button onclick="addChatPanel()">Add Chat Panel</button>
        <button onclick="removeChatPanel()">Remove Chat Panel</button>
        <button onclick="addOtherContent()">Add Other Content</button>
        <button onclick="removeOtherContent()">Remove Other Content</button>
        <button onclick="hideOtherContent()">Hide Other Content (hidden attr)</button>
        <button onclick="showOtherContent()">Show Other Content</button>
        <div id="chat-result" class="result"></div>
    </div>

    <script>
        // Copy the relevant functions from content.js for testing
        function hasChatPanel() {
            const liveChatFrame = document.querySelector('ytd-live-chat-frame');
            const liveChatRenderer = document.querySelector('ytd-live-chat-renderer');
            const chatFrame = document.querySelector('#chatframe');
            const chatContainer = document.querySelector('ytd-watch-flexy[theater] #chat-container');

            return !!(liveChatFrame || liveChatRenderer || chatFrame || chatContainer);
        }

        function sidebarContainsOnlyRelatedVideos() {
            const sidebar = document.querySelector('#secondary');
            if (!sidebar) return false;

            // Helper function to check if element exists and is not hidden
            const isElementVisible = (selector) => {
                const elements = sidebar.querySelectorAll(selector);
                return Array.from(elements).some(el =>
                    !el.hasAttribute('hidden') &&
                    el.style.display !== 'none' &&
                    getComputedStyle(el).display !== 'none'
                );
            };

            // Check for non-related content in sidebar
            const hasOtherContent = isElementVisible('#other-content');
            const hasChat = isElementVisible('ytd-live-chat-frame, ytd-live-chat-renderer, #chatframe');

            // If sidebar has any non-related content, return false
            if (hasOtherContent || hasChat) {
                return false;
            }

            // Check if sidebar has related videos content
            const hasRelatedVideos = isElementVisible('ytd-watch-next-secondary-results-renderer');

            // Return true only if sidebar exists, has related videos, but no other content
            return !!hasRelatedVideos;
        }

        function getHidingStrategy(setting) {
            // Handle boolean checkbox value
            if (!setting) {
                return { hideRelatedVideos: false, hideSidebar: false };
            }

            // Hide related videos, and hide entire sidebar if no other content remains
            const hasOtherContent = !sidebarContainsOnlyRelatedVideos();
            return {
                hideRelatedVideos: true,
                hideSidebar: !hasOtherContent // Hide sidebar only if no other content left
            };
        }

        function addChatPanel() {
            const container = document.getElementById('chat-container');
            container.innerHTML = '<ytd-live-chat-frame class="chat-panel">Live Chat Panel</ytd-live-chat-frame>';
            testChatDetection();
            testSidebarLogic();
        }

        function removeChatPanel() {
            const container = document.getElementById('chat-container');
            container.innerHTML = '';
            testChatDetection();
            testSidebarLogic();
        }

        function testChatDetection() {
            const hasChat = hasChatPanel();
            document.getElementById('chat-result').textContent = 
                `Chat panel detected: ${hasChat}`;
        }

        function testSidebarLogic() {
            const setting = document.getElementById('hideSidebarTest').checked;
            const strategy = getHidingStrategy(setting);
            const sidebar = document.querySelector('#secondary');
            const relatedVideos = document.querySelector('ytd-watch-next-secondary-results-renderer');
            const onlyRelatedVideos = sidebarContainsOnlyRelatedVideos();
            const hasChat = hasChatPanel();

            // Apply the hiding strategy
            if (relatedVideos) {
                relatedVideos.style.display = strategy.hideRelatedVideos ? 'none' : 'block';
            }
            if (sidebar) {
                sidebar.style.display = strategy.hideSidebar ? 'none' : 'block';
            }

            document.getElementById('sidebar-result').innerHTML =
                `Setting: <strong>${setting}</strong><br>` +
                `Has chat: <strong>${hasChat}</strong><br>` +
                `Only related videos: <strong>${onlyRelatedVideos}</strong><br>` +
                `Hide related videos: <strong>${strategy.hideRelatedVideos}</strong><br>` +
                `Hide entire sidebar: <strong>${strategy.hideSidebar}</strong><br>` +
                `Related videos: <strong>${strategy.hideRelatedVideos ? 'HIDDEN' : 'VISIBLE'}</strong><br>` +
                `Sidebar: <strong>${strategy.hideSidebar ? 'HIDDEN' : 'VISIBLE'}</strong>`;
        }

        function addChatPanel() {
            const chatContainer = document.getElementById('chat-container');
            chatContainer.innerHTML = '<ytd-live-chat-frame>Simulated Chat Panel</ytd-live-chat-frame>';
            document.getElementById('chat-result').textContent = 'Chat panel added. Has chat: ' + hasChatPanel();
            testSidebarLogic(); // Update sidebar logic
        }

        function removeChatPanel() {
            const chatContainer = document.getElementById('chat-container');
            chatContainer.innerHTML = '';
            document.getElementById('chat-result').textContent = 'Chat panel removed. Has chat: ' + hasChatPanel();
            testSidebarLogic(); // Update sidebar logic
        }

        function addOtherContent() {
            const otherContent = document.getElementById('other-content');
            if (otherContent) {
                otherContent.style.display = 'block';
            }
            document.getElementById('chat-result').textContent = 'Other content added.';
            testSidebarLogic(); // Update sidebar logic
        }

        function removeOtherContent() {
            const otherContent = document.getElementById('other-content');
            if (otherContent) {
                otherContent.style.display = 'none';
            }
            document.getElementById('chat-result').textContent = 'Other content removed.';
            testSidebarLogic(); // Update sidebar logic
        }

        function hideOtherContent() {
            const otherContent = document.getElementById('other-content');
            if (otherContent) {
                otherContent.setAttribute('hidden', '');
            }
            document.getElementById('chat-result').textContent = 'Other content hidden (hidden attribute).';
            testSidebarLogic(); // Update sidebar logic
        }

        function showOtherContent() {
            const otherContent = document.getElementById('other-content');
            if (otherContent) {
                otherContent.removeAttribute('hidden');
                otherContent.style.display = 'block';
            }
            document.getElementById('chat-result').textContent = 'Other content shown.';
            testSidebarLogic(); // Update sidebar logic
        }

        function testChatDetection() {
            document.getElementById('chat-result').textContent = 'Has chat panel: ' + hasChatPanel();
        }

        // Initialize
        testChatDetection();
        testSidebarLogic();
    </script>
</body>
</html>
