<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Keyboard Shortcut Test - YouTube Reading Friendly Extension</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        
        .shortcut-display {
            font-family: 'Courier New', monospace;
            background-color: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            display: inline-block;
            margin: 0 5px;
        }
        
        .test-instructions {
            background-color: #e7f3ff;
            padding: 15px;
            border-left: 4px solid #007bff;
            margin: 15px 0;
        }
        
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        .icon-status {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        
        .icon-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid #ccc;
        }
        
        .icon-active {
            background-color: #28a745;
            border-color: #28a745;
        }
        
        .icon-inactive {
            background-color: #6c757d;
            border-color: #6c757d;
        }
    </style>
</head>
<body>
    <h1>🎯 Keyboard Shortcut Test</h1>
    <p>This page tests the keyboard shortcut functionality for the YouTube Reading Friendly extension.</p>
    
    <div class="test-section">
        <h2>📋 Test Instructions</h2>
        <div class="test-instructions">
            <ol>
                <li>Make sure the YouTube Reading Friendly extension is installed and enabled</li>
                <li>Open this page on a YouTube video page (e.g., youtube.com/watch?v=...)</li>
                <li>Press the keyboard shortcut: <span class="shortcut-display">Alt + R</span></li>
                <li>Observe the extension icon in the browser toolbar</li>
                <li>The icon should change between active/inactive states</li>
            </ol>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🔧 Current Extension Status</h2>
        <div id="extension-status" class="status info">
            Checking extension status...
        </div>
        
        <div class="icon-status">
            <span>Extension Icon Status:</span>
            <div id="icon-indicator" class="icon-indicator icon-inactive"></div>
            <span id="icon-status-text">Unknown</span>
        </div>
        
        <button onclick="checkExtensionStatus()">🔄 Refresh Status</button>
        <button onclick="simulateShortcut()">⌨️ Simulate Alt+R</button>
    </div>
    
    <div class="test-section">
        <h2>📊 Test Results</h2>
        <div id="test-results">
            <div class="status info">
                Press <span class="shortcut-display">Alt + R</span> to test the keyboard shortcut functionality.
            </div>
        </div>
        
        <div id="shortcut-log" style="margin-top: 15px;">
            <h4>Shortcut Activity Log:</h4>
            <div id="log-content" style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; min-height: 100px; max-height: 200px; overflow-y: auto;">
                <div style="color: #6c757d;">Waiting for keyboard shortcuts...</div>
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>🎯 Expected Behavior</h2>
        <ul>
            <li><strong>First Alt+R press:</strong> Should toggle reading mode ON and change icon to active state</li>
            <li><strong>Second Alt+R press:</strong> Should toggle reading mode OFF and change icon to inactive state</li>
            <li><strong>Icon updates:</strong> Should happen immediately when shortcut is pressed</li>
            <li><strong>Storage sync:</strong> Settings should be saved to Chrome storage</li>
        </ul>
    </div>

    <script src="config.js"></script>
    <script>
        let logCounter = 0;
        
        function addToLog(message, type = 'info') {
            const logContent = document.getElementById('log-content');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#495057';
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            // Remove "waiting" message if it exists
            if (logCounter === 0) {
                logContent.innerHTML = '';
            }
            
            logContent.appendChild(logEntry);
            logContent.scrollTop = logContent.scrollHeight;
            logCounter++;
        }
        
        function checkExtensionStatus() {
            const statusDiv = document.getElementById('extension-status');
            const iconIndicator = document.getElementById('icon-indicator');
            const iconStatusText = document.getElementById('icon-status-text');
            
            // Check if we're on YouTube
            if (!window.location.hostname.includes('youtube.com')) {
                statusDiv.className = 'status warning';
                statusDiv.innerHTML = '⚠️ This test should be run on a YouTube page for full functionality.';
                return;
            }
            
            // Try to access Chrome extension APIs
            if (typeof chrome !== 'undefined' && chrome.storage) {
                chrome.storage.sync.get('readingMode', function(data) {
                    const isEnabled = data.readingMode || false;
                    
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `✅ Extension detected! Reading mode is currently: <strong>${isEnabled ? 'ENABLED' : 'DISABLED'}</strong>`;
                    
                    // Update icon indicator
                    iconIndicator.className = `icon-indicator ${isEnabled ? 'icon-active' : 'icon-inactive'}`;
                    iconStatusText.textContent = isEnabled ? 'Active' : 'Inactive';
                    
                    addToLog(`Extension status checked: Reading mode ${isEnabled ? 'ENABLED' : 'DISABLED'}`, 'success');
                });
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '❌ Extension not detected or not accessible from this context.';
                addToLog('Extension not detected', 'error');
            }
        }
        
        function simulateShortcut() {
            // Create a synthetic keyboard event
            const event = new KeyboardEvent('keydown', {
                key: 'r',
                altKey: true,
                bubbles: true,
                cancelable: true
            });
            
            document.dispatchEvent(event);
            addToLog('Simulated Alt+R keypress', 'info');
        }
        
        // Listen for keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            // Check for Alt+R
            if (event.altKey && event.key.toLowerCase() === 'r') {
                event.preventDefault();
                addToLog('Alt+R detected! Checking for reading mode toggle...', 'success');
                
                // Check status after a short delay to see if it changed
                setTimeout(() => {
                    checkExtensionStatus();
                }, 100);
            }
        });
        
        // Listen for storage changes to detect when reading mode is toggled
        if (typeof chrome !== 'undefined' && chrome.storage) {
            chrome.storage.onChanged.addListener(function(changes, namespace) {
                if (changes.readingMode) {
                    const newValue = changes.readingMode.newValue;
                    const oldValue = changes.readingMode.oldValue;
                    addToLog(`Reading mode changed: ${oldValue} → ${newValue}`, 'success');
                    
                    // Update the status display
                    setTimeout(checkExtensionStatus, 50);
                }
            });
        }
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            checkExtensionStatus();
            addToLog('Test page loaded and ready', 'info');
        });
    </script>
</body>
</html>
