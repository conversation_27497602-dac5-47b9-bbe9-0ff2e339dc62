# Development Guide

## Console Logging Configuration

This extension includes a development mode system that controls when `console.log` statements are active.

### How it works

- All console logging is controlled by the `config.js` file
- In development mode, console.log statements will work normally
- In production mode, console.log statements are silenced (but console.warn and console.error still work)

### Switching between Development and Production

#### For Development (console.log enabled):
Edit `config.js` and set:
```javascript
window.CONFIG = {
  isDevelopment: true,  // Enable console.log
  enableDebugMode: true,
  enableVerboseLogging: true
};
```

#### For Production (console.log disabled):
Edit `config.js` and set:
```javascript
window.CONFIG = {
  isDevelopment: false,  // Disable console.log
  enableDebugMode: false,
  enableVerboseLogging: false
};
```

### Console Methods Behavior

| Method | Development | Production | Notes |
|--------|-------------|------------|-------|
| `console.log()` | ✅ Active | ❌ Silent | Controlled by `isDevelopment` |
| `console.info()` | ✅ Active | ❌ Silent | Controlled by `isDevelopment` |
| `console.debug()` | ✅ Active | ❌ Silent | Controlled by `isDevelopment` AND `enableDebugMode` |
| `console.warn()` | ✅ Active | ✅ Active | Always active (important for production) |
| `console.error()` | ✅ Active | ✅ Active | Always active (important for production) |

### Files Affected

The development configuration is loaded in:
- `content.js` (via manifest.json content_scripts)
- `options.html` (via script tag)
- `popup.html` (via script tag)

### Build Process

When preparing for production:

1. Set `isDevelopment: false` in `config.js`
2. Test the extension to ensure no console.log output appears
3. Package the extension for distribution

### Adding New Console Statements

When adding new console.log statements to the code:
- Use `console.log()` for general debugging information
- Use `console.debug()` for detailed debugging that should only appear when `enableDebugMode` is true
- Use `console.warn()` for warnings that should appear in production
- Use `console.error()` for errors that should appear in production

### Alternative: Environment-based Configuration

For more advanced setups, you could modify `config.js` to detect the environment automatically:

```javascript
window.CONFIG = {
  // Automatically detect development mode
  isDevelopment: !('update_url' in chrome.runtime.getManifest()),
  enableDebugMode: true,
  enableVerboseLogging: true
};
```

This approach uses the fact that extensions loaded from the Chrome Web Store have an `update_url` in their manifest, while locally loaded extensions do not.
