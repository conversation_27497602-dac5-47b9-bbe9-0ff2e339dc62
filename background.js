// Background script for YouTube Reading Friendly extension

// Initialize extension when installed
chrome.runtime.onInstalled.addListener(function(details) {
  if (details.reason === 'install') {
    // Set default settings
    chrome.storage.sync.set({
      readingMode: false,
      fontSize: 'medium',
      fontFamily: 'youtube-sans',
      lineHeight: 'normal',
      htmlFontSize: 'default',
      hideMiniGuide: true,
      guideStyle: 'overlay'
    });

    // Open options page on install
    chrome.tabs.create({
      url: 'options.html'
    });
  }
});

// Listen for tab updates to update icon state
chrome.tabs.onUpdated.addListener(function(tabId, changeInfo, tab) {
  // Only proceed if the tab has completed loading and is a YouTube page
  if (changeInfo.status === 'complete' && tab.url && tab.url.includes('youtube.com')) {
    // Check if reading mode is enabled
    chrome.storage.sync.get('readingMode', function(data) {
      updateIcon(data.readingMode);
    });
  }
});

// Listen for messages from content script
chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
  if (message.action === 'updateIcon') {
    updateIcon(message.enabled);
    sendResponse({ success: true });
  }
});

// Update icon based on reading mode state
function updateIcon(enabled) {
  const iconPath = enabled ? 'icons/icon_active_' : 'icons/icon_';
  chrome.action.setIcon({
    path: {
      16: `${iconPath}16.png`,
      48: `${iconPath}48.png`,
      128: `${iconPath}128.png`
    }
  });
}
